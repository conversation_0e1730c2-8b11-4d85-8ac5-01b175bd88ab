import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Glossário do Café | Termos e Definições - Cereja",
  description: "Glossário completo com termos técnicos do café: métodos de preparo, variedades, processamento e muito mais. Aprenda sobre espresso, pour over, fermentação e outras técnicas.",
  keywords: [
    "glossário café",
    "termos café",
    "métodos preparo café",
    "espresso",
    "pour over",
    "v60",
    "chemex",
    "aeropress",
    "café especial",
    "barista",
    "fermentação café",
    "variedades café",
    "arábica",
    "robusta",
    "processamento café",
    "torra café"
  ],
  openGraph: {
    title: "Glossário do Café | Termos e Definições - Cereja",
    description: "Glossário completo com termos técnicos do café: métodos de preparo, variedades, processamento e muito mais.",
    images: ["/cereja.png"],
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app/glossario",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function GlossarioLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const glossarySchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Glossário do Café",
    "description": "Glossário completo com termos técnicos do café",
    "url": "https://cereja-app.vercel.app/glossario",
    "isPartOf": {
      "@id": "https://cereja-app.vercel.app/#website"
    },
    "about": {
      "@type": "Thing",
      "name": "Terminologia do Café"
    },
    "mainEntity": {
      "@type": "DefinedTermSet",
      "name": "Termos do Café",
      "description": "Coleção de termos e definições relacionados ao café"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(glossarySchema)
        }}
      />
      {children}
    </>
  );
}
