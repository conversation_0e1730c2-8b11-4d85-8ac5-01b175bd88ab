"use client";

import { memo, useCallback } from "react";
import { Play, Pause, RotateCcw } from "lucide-react";

interface TimerProps {
  tempo: number;
  rodando: boolean;
  contandoRegressivo: boolean;
  contagemRegressiva: number;
  tempoTotal: number;
  modalAberto: boolean;
  onIniciarPausar: () => void;
  onResetar: () => void;
  onAbrirModal: () => void;
}

const Timer = memo(function Timer({
  tempo,
  rodando,
  contandoRegressivo,
  contagemRegressiva,
  tempoTotal,
  modalAberto,
  onIniciarPausar,
  onResetar,
  onAbrirModal,
}: TimerProps) {
  const formatarTempo = useCallback((segundos: number): string => {
    const mins = Math.floor(segundos / 60);
    const secs = segundos % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const receitaFinalizada = tempo >= tempoTotal && !contandoRegressivo;

  return (
    <div className="bg-card p-4 sm:p-6 rounded-lg border border-border shadow-md">
      <h2 className="text-xs sm:text-sm font-medium text-foreground mb-3 sm:mb-4">
        Cronômetro
      </h2>

      {/* Display do tempo */}
      <div className="flex flex-col items-center mb-1 sm:mb-2">
        <div className={`text-2xl sm:text-3xl font-bold px-3 sm:px-4 py-2 sm:py-3 rounded-lg border tabular-nums tracking-wider mb-2 transition-all duration-500 ${
          receitaFinalizada
            ? "text-primary-foreground bg-primary border-primary shadow-lg animate-pulse"
            : tempo > 0 && !rodando && !contandoRegressivo
            ? "text-foreground bg-secondary border-border animate-pulse"
            : "text-foreground bg-secondary border-border"
        }`}>
          {contandoRegressivo ? contagemRegressiva : formatarTempo(tempo)}
        </div>

        {receitaFinalizada && (
          <div className="text-center space-y-2 animate-in fade-in duration-700">
            <div className="text-lg sm:text-xl font-semibold text-primary">
              ✨ Receita Finalizada! ✨
            </div>
            <div className="text-sm sm:text-base text-muted-foreground">
              Aguarde a extração terminar e aproveite seu café! ☕
            </div>
          </div>
        )}
      </div>

      {/* Botões de controle */}
      <div className="flex justify-center gap-2 sm:gap-3">
        {receitaFinalizada ? (
          // Receita finalizada: botões Compartilhar e Reiniciar
          <>
            <button
              onClick={onAbrirModal}
              className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-secondary text-secondary-foreground rounded-md text-xs font-normal hover:bg-accent hover:text-accent-foreground transition-colors min-h-touch touch-target"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="sm:w-4 sm:h-4">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                <polyline points="16,6 12,2 8,6"/>
                <line x1="12" y1="2" x2="12" y2="15"/>
              </svg>
              Compartilhar
            </button>
            <button
              onClick={onResetar}
              className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-primary text-primary-foreground rounded-md text-xs font-normal hover:bg-primary/90 transition-colors min-h-touch touch-target"
            >
              <RotateCcw size={18} className="sm:w-4 sm:h-4" />
              Reiniciar
            </button>
          </>
        ) : (
          // Receita em andamento: botões normais
          <>
            <button
              onClick={onIniciarPausar}
              className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-primary text-primary-foreground rounded-md text-xs font-normal hover:bg-primary/90 transition-colors min-h-touch touch-target"
            >
              {rodando ? (
                <>
                  <Pause size={18} className="sm:w-4 sm:h-4" />
                  Pausar
                </>
              ) : contandoRegressivo ? (
                <>
                  <Pause size={18} className="sm:w-4 sm:h-4" />
                  Cancelar
                </>
              ) : tempo > 0 ? (
                <>
                  <Play size={18} className="sm:w-4 sm:h-4" />
                  Retomar
                </>
              ) : (
                <>
                  <Play size={18} className="sm:w-4 sm:h-4" />
                  Iniciar
                </>
              )}
            </button>

            {(tempo > 0 || rodando) && !contandoRegressivo && (
              <button
                onClick={onResetar}
                className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-secondary text-secondary-foreground rounded-md text-xs font-normal hover:bg-accent hover:text-accent-foreground transition-colors min-h-touch touch-target"
              >
                <RotateCcw size={18} className="sm:w-4 sm:h-4" />
                Reiniciar
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
});

export default Timer;
