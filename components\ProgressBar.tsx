"use client";

import { memo } from "react";

interface ProgressBarProps {
  tempo: number;
  tempoTotal: number;
  receita: Array<{
    tempo: string;
    tempoSegundos: number;
    quantidade: number;
    total: number;
  }>;
  passoAtual: number;
  rodando: boolean;
  contandoRegressivo: boolean;
}

const ProgressBar = memo(function ProgressBar({
  tempo,
  tempoTotal,
  receita,
  passoAtual,
  rodando,
  contandoRegressivo,
}: ProgressBarProps) {
  if ((!rodando && tempo === 0) || contandoRegressivo) {
    return null;
  }

  return (
    <div className="w-full mt-4">
      <div className="relative">
        {/* Linha de fundo */}
        <div className="w-full h-1 bg-secondary rounded-full"></div>

        {/* Linha de progresso */}
        <div
          className="absolute top-0 h-1 bg-primary rounded-full transition-all duration-1000 ease-out"
          style={{
            width: `${Math.min((tempo / tempoTotal) * 100, 100)}%`
          }}
        />

        {/* Marcadores dos passos */}
        {receita.map((passo, index) => {
          const posicao = (passo.tempoSegundos / tempoTotal) * 100;
          const isConcluido = index <= passoAtual;
          const isAtivo = index === passoAtual;

          return (
            <div
              key={index}
              className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
              style={{ left: `${posicao}%` }}
            >
              <div
                className={`w-0.5 h-2 transition-all duration-500 ${
                  isAtivo
                    ? 'bg-primary scale-110 shadow-md'
                    : isConcluido
                    ? 'bg-primary'
                    : 'bg-muted-foreground/60'
                }`}
              />
            </div>
          );
        })}

        {/* Marcador final */}
        <div
          className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
          style={{ left: '100%' }}
        >
          <div
            className={`w-0.5 h-2 transition-all duration-500 ${
              tempo >= tempoTotal
                ? 'bg-primary'
                : 'bg-muted-foreground/60'
            }`}
          />
        </div>
      </div>
    </div>
  );
});

export default ProgressBar;
