"use client";

import { memo, forwardRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { X, Home as HomeIcon, BookOpen, Coffee, Settings, Database } from "lucide-react";

interface SidebarProps {
  isOpen: boolean;
  isClosing: boolean;
  onClose: () => void;
}

const Sidebar = memo(forwardRef<HTMLDivElement, SidebarProps>(
  function Sidebar({ isOpen, isClosing, onClose }, ref) {
    if (!isOpen) return null;

    return (
      <>
        {/* Overlay */}
        <div
          className={`fixed inset-0 bg-black/50 z-40 ${isClosing ? 'animate-fade-out' : 'animate-fade-in'}`}
          onClick={onClose}
        />

        {/* Sidebar content */}
        <div
          ref={ref}
          className={`fixed top-0 left-0 h-full w-80 bg-card border-r border-border shadow-lg z-50 ${isClosing ? 'animate-slide-out-left' : 'animate-slide-in-left'}`}
        >
          {/* Sidebar header */}
          <div className="flex items-center justify-between p-6 border-b border-border">
            <div className="flex items-center gap-3">
              <Image
                src="/cereja.png"
                alt="Cereja"
                width={40}
                height={40}
                className="object-contain"
                quality={75}
              />
              <div>
                <h2 className="text-xl font-bold text-foreground">cereja</h2>
                <p className="text-sm text-muted-foreground">sua calculadora pessoal ♥</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Navigation menu */}
          <nav className="p-6">
            <div className="space-y-2">
              <Link
                href="/"
                onClick={onClose}
                className="flex items-center gap-3 px-4 py-3 text-primary bg-primary/10 rounded-lg transition-colors"
              >
                <HomeIcon size={20} />
                <span className="font-medium">Calculadora 4:6</span>
              </Link>

              <Link
                href="/glossario"
                onClick={onClose}
                className="flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
              >
                <BookOpen size={20} />
                <span className="font-medium">Glossário</span>
              </Link>

              {/* Links desabilitados - Em Construção */}
              <div
                title="Em Construção"
                className="flex items-center gap-3 px-4 py-3 text-muted-foreground/50 rounded-lg opacity-50 cursor-not-allowed pointer-events-none"
              >
                <Settings size={20} />
                <span className="font-medium">
                  Método 4:6 <span className="text-xs">(Em Construção)</span>
                </span>
              </div>

              <div
                title="Em Construção"
                className="flex items-center gap-3 px-4 py-3 text-muted-foreground/50 rounded-lg opacity-50 cursor-not-allowed pointer-events-none"
              >
                <Database size={20} />
                <span className="font-medium">
                  Base de Conhecimento <span className="text-xs">(Em Construção)</span>
                </span>
              </div>
            </div>

            {/* Botão de doação */}
            <div className="mt-6 pt-6 border-t border-border">
              <a
                href="https://ko-fi.com/pedrogott"
                target="_blank"
                rel="noopener noreferrer"
                onClick={onClose}
                className="w-full flex items-center gap-3 px-4 py-3 text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-400/30 transform hover:scale-[1.02]"
              >
                <Coffee size={20} />
                <div className="flex flex-col text-left">
                  <span className="font-medium text-sm">Gostou do projeto?</span>
                  <span className="text-xs opacity-80">Pague-me um café!</span>
                </div>
              </a>
            </div>

            {/* Link de Política de Privacidade - discreto */}
            <div className="mt-4 pt-4 border-t border-border/50">
              <Link
                href="/privacidade"
                onClick={onClose}
                className="block px-4 py-2 text-xs text-muted-foreground hover:text-muted-foreground/80 transition-colors text-center"
              >
                Política de Privacidade
              </Link>
            </div>
          </nav>
        </div>
      </>
    );
  }
));

export default Sidebar;
