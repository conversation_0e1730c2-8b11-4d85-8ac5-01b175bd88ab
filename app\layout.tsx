import type { Metada<PERSON>, Viewport } from "next";
import { Outfit } from "next/font/google";
import "./globals.css";
import { GoogleAnalytics } from '@/components/GoogleAnalytics';
import { GoogleAdSense } from '@/components/GoogleAdSense';
import { ConsentManager } from '@/components/ConsentManager';
import BackToTop from '@/components/BackToTop';

const outfit = Outfit({
  variable: "--font-outfit",
  subsets: ["latin"],
  display: "swap", // Critical for performance
  preload: true,
});

export const metadata: Metadata = {
  metadataBase: new URL("https://cereja-app.vercel.app"),
  title: {
    default: "Cereja - Calculadora 4:6 | Método Tetsu Kasuya",
    template: "%s | Cereja",
  },
  description: "Calculadora gratuita para o método 4:6 de Tetsu Kasuya. Crie receitas perfeitas de café coado com controle de acidez, doçura e corpo. Cronômetro integrado e receitas personalizadas.",
  keywords: [
    "método 4:6",
    "Tetsu Kasuya",
    "calculadora café",
    "café coado",
    "pour over",
    "v60",
    "receita café",
    "café especial",
    "barista",
    "coffee brewing"
  ],
  authors: [{ name: "Pedro Gottardi" }],
  creator: "Pedro Gottardi",
  publisher: "Cereja",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "pt_BR",
    url: "https://cereja-app.vercel.app",
    title: "Cereja - Calculadora 4:6 | Método Tetsu Kasuya",
    description: "Calculadora gratuita para o método 4:6 de Tetsu Kasuya. Crie receitas perfeitas de café coado com controle de acidez, doçura e corpo.",
    images: [
      {
        url: "/cereja.png",
        width: 512,
        height: 512,
        alt: "Cereja - Calculadora do método 4:6",
      },
    ],
    siteName: "Cereja",
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Cereja - Calculadora 4:6 | Método Tetsu Kasuya',
    description: 'Calculadora gratuita para o método 4:6 de Tetsu Kasuya. Crie receitas perfeitas de café coado.',
    images: ['/cereja.png'],
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app",
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Cereja",
  },
  formatDetection: {
    telephone: false,
  },
  other: {
    'theme-color': '#8b5cf6',
    'msapplication-TileColor': '#8b5cf6',
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: "#8b5cf6",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://cereja-app.vercel.app/#organization",
    "name": "Cereja",
    "url": "https://cereja-app.vercel.app",
    "logo": {
      "@type": "ImageObject",
      "url": "https://cereja-app.vercel.app/cereja.png",
      "width": 512,
      "height": 512
    },
    "description": "Calculadora gratuita para o método 4:6 de Tetsu Kasuya",
    "foundingDate": "2024",
    "sameAs": [
      "https://ko-fi.com/pedrogott"
    ]
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": "https://cereja-app.vercel.app/#website",
    "url": "https://cereja-app.vercel.app",
    "name": "Cereja",
    "description": "Calculadora para o método 4:6 de Tetsu Kasuya",
    "publisher": {
      "@id": "https://cereja-app.vercel.app/#organization"
    },
    "inLanguage": "pt-BR"
  };

  const webApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Cereja - Calculadora 4:6",
    "description": "Calculadora gratuita para o método 4:6 de Tetsu Kasuya com cronômetro integrado",
    "url": "https://cereja-app.vercel.app",
    "applicationCategory": "UtilitiesApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "BRL"
    },
    "author": {
      "@type": "Person",
      "name": "Pedro Gottardi"
    }
  };

  return (
    <html lang="pt-BR">
      <head>
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteSchema)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(webApplicationSchema)
          }}
        />

        {/* Preload critical resources */}
        <link
          rel="preload"
          href="/cereja.png"
          as="image"
          type="image/png"
          fetchPriority="high"
        />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />
        <link rel="dns-prefetch" href="//pagead2.googlesyndication.com" />
      </head>
      <body
        className={`${outfit.variable} antialiased`}
      >
        <GoogleAnalytics />
        <GoogleAdSense />
        {children}
        <ConsentManager />
        <BackToTop />
      </body>
    </html>
  );
}
