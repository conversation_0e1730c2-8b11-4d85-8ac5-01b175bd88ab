import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Política de Privacidade | Cereja",
  description: "Política de privacidade da Cereja - Calculadora 4:6. Saiba como coletamos, usamos e protegemos seus dados pessoais. Informações sobre cookies, analytics e consentimento.",
  keywords: [
    "política de privacidade",
    "proteção de dados",
    "cookies",
    "LGPD",
    "GDPR",
    "privacidade",
    "dados pessoais",
    "consentimento",
    "analytics"
  ],
  openGraph: {
    title: "Política de Privacidade - Cereja",
    description: "Política de privacidade da Cereja - Calculadora 4:6. Saiba como protegemos seus dados pessoais.",
    images: ["/cereja.png"],
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app/privacidade",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function PrivacidadeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const privacyPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Política de Privacidade",
    "description": "Política de privacidade da Cereja - Calculadora 4:6",
    "url": "https://cereja-app.vercel.app/privacidade",
    "isPartOf": {
      "@id": "https://cereja-app.vercel.app/#website"
    },
    "about": {
      "@type": "Thing",
      "name": "Proteção de Dados Pessoais"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(privacyPageSchema)
        }}
      />
      {children}
    </>
  );
}
