"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, useRef, useCallback } from "react";
import { Menu, Home as HomeIcon, BookOpen, Coffee, ArrowLeft, Search } from "lucide-react";

export default function NotFound() {
  const [sidebarAberta, setSidebarAberta] = useState<boolean>(false);
  const [sidebarFechando, setSidebarFechando] = useState<boolean>(false);
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Alterar título da página
  useEffect(() => {
    document.title = "Cereja - Página não encontrada";
  }, []);

  // Função para fechar sidebar com animação
  const fecharSidebar = useCallback(() => {
    setSidebarFechando(true);
    setTimeout(() => {
      setSidebarAberta(false);
      setSidebarFechando(false);
    }, 300);
  }, []);

  // Fechar sidebar ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        fecharSidebar();
      }
    };

    if (sidebarAberta) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarAberta, fecharSidebar]);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Header com menu hambúrguer */}
      <header className="fixed sm:absolute top-4 left-4 sm:top-6 sm:left-6 z-30">
        <button
          onClick={() => setSidebarAberta(true)}
          className="flex items-center gap-2 p-2 text-muted-foreground hover:text-primary hover:bg-accent/50 rounded-lg sm:rounded-lg rounded-full sm:bg-transparent bg-card/90 backdrop-blur-sm shadow-lg sm:shadow-none border sm:border-0 border-border/20 transition-all"
        >
          <Menu size={20} />
          <span className="text-sm font-medium hidden sm:inline">Menu</span>
        </button>
      </header>

      {/* Sidebar */}
      {sidebarAberta && (
        <div className="fixed inset-0 z-40">
          {/* Overlay */}
          <div
            className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${
              sidebarFechando ? 'animate-[fadeOut_0.3s_ease-out]' : 'animate-[fadeIn_0.3s_ease-out]'
            }`}
            onClick={fecharSidebar}
          />

          {/* Sidebar content */}
          <div
            ref={sidebarRef}
            className={`absolute left-0 top-0 h-full w-80 max-w-[85vw] bg-sidebar border-r border-sidebar-border shadow-xl transition-transform duration-300 ${
              sidebarFechando ? 'animate-[slideOutToLeft_0.3s_ease-out]' : 'animate-[slideInFromLeft_0.3s_ease-out]'
            }`}
          >
            {/* Header da sidebar */}
            <div className="flex items-center justify-between p-6 border-b border-sidebar-border">
              <div className="flex items-center gap-3">
                <Image
                  src="/cereja.png"
                  alt="Cereja"
                  width={32}
                  height={32}
                  className="object-contain"
                  quality={90}
                />
                <div>
                  <h2 className="text-lg font-semibold text-sidebar-foreground">cereja</h2>
                  <p className="text-xs text-sidebar-foreground/70">calculadora 4:6</p>
                </div>
              </div>
            </div>

            {/* Navigation menu */}
            <nav className="p-6">
              <div className="space-y-2">
                <Link
                  href="/"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent rounded-lg transition-colors"
                >
                  <HomeIcon size={20} />
                  <span className="font-medium">Calculadora 4:6</span>
                </Link>

                <Link
                  href="/glossario"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent rounded-lg transition-colors"
                >
                  <BookOpen size={20} />
                  <span className="font-medium">Glossário</span>
                </Link>
              </div>

              {/* Seção de privacidade */}
              <div className="mt-8 pt-6 border-t border-sidebar-border">
                <div className="space-y-1">
                  <Link
                    href="/privacidade"
                    onClick={fecharSidebar}
                    className="block px-4 py-2 text-xs text-sidebar-foreground/70 hover:text-sidebar-foreground transition-colors"
                  >
                    Política de Privacidade
                  </Link>
                </div>
              </div>
            </nav>
          </div>
        </div>
      )}

      {/* Conteúdo principal */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-8 py-16 sm:py-24">
        {/* Ícone de café grande */}
        <div className="mb-8 sm:mb-12">
          <div className="relative">
            <Coffee 
              size={120} 
              className="text-muted-foreground/30 sm:w-32 sm:h-32 md:w-40 md:h-40" 
            />
            <div className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center text-lg sm:text-xl font-bold">
              !
            </div>
          </div>
        </div>

        {/* Título e mensagem */}
        <div className="text-center max-w-md mx-auto">
          <h1 className="text-6xl sm:text-8xl font-bold text-foreground mb-4">
            404
          </h1>
          
          <h2 className="text-xl sm:text-2xl font-semibold text-foreground mb-4">
            Página não encontrada
          </h2>
          
          <p className="text-muted-foreground mb-8 leading-relaxed">
            Ops! Parece que esta página não existe ou foi movida. 
            Que tal voltar para a calculadora e preparar um café delicioso?
          </p>

          {/* Botões de ação */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-primary text-primary-foreground font-medium rounded-lg hover:bg-primary/90 transition-colors min-h-touch"
            >
              <HomeIcon size={18} />
              Voltar ao início
            </Link>
            
            <Link
              href="/glossario"
              className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-secondary text-secondary-foreground font-medium rounded-lg hover:bg-secondary/80 transition-colors min-h-touch"
            >
              <Search size={18} />
              Explorar glossário
            </Link>
          </div>
        </div>

        {/* Sugestões úteis */}
        <div className="mt-12 sm:mt-16 max-w-lg mx-auto">
          <h3 className="text-sm font-medium text-muted-foreground mb-4 text-center">
            Você pode estar procurando por:
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Link
              href="/"
              className="flex items-center gap-3 p-4 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors"
            >
              <Coffee size={20} className="text-primary" />
              <div>
                <div className="font-medium text-foreground">Calculadora 4:6</div>
                <div className="text-xs text-muted-foreground">Método Tetsu Kasuya</div>
              </div>
            </Link>
            
            <Link
              href="/glossario"
              className="flex items-center gap-3 p-4 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors"
            >
              <BookOpen size={20} className="text-primary" />
              <div>
                <div className="font-medium text-foreground">Glossário</div>
                <div className="text-xs text-muted-foreground">Termos do café</div>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-secondary/50 py-4 w-full text-center">
        <p className="text-xs text-muted-foreground">
          Desenvolvido por{" "}
          <a
            href="https://ko-fi.com/pedrogott"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 transition-colors font-medium"
          >
            Pedro Gottardi
          </a>
        </p>
      </footer>
    </div>
  );
}
