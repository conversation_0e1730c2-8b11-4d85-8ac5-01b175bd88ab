"use client";
"use no memo";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, useRef, useCallback, Fragment } from "react";
import { Lock, Unlock, Play, Pause, RotateCcw, Menu, X, Home as HomeIcon, BookOpen, Coffee, Settings, Database } from "lucide-react";
import {
  trackButtonClick,
  trackRecipeGenerated,
  trackRecipeCompleted,
  trackRecipeTimer
} from "@/lib/analytics";
import Timer from "@/components/Timer";
import ProgressBar from "@/components/ProgressBar";
import RecipeSteps from "@/components/RecipeSteps";
import ShareModal from "@/components/ShareModal";
import Sidebar from "@/components/Sidebar";
import ProfileButton from "@/components/ProfileButton";
import Footer from "@/components/Footer";

function Home() {
  const [cafe, setCafe] = useState<string>("15");
  const [agua, setAgua] = useState<string>("225");
  const [proporcao, setProporcao] = useState<string>("15");
  const [perfilSabor, setPerfilSabor] = useState<string>("Equilibrado");
  const [perfilCorpo, setPerfilCorpo] = useState<string>("Equilibrado");
  const [aguaBloqueada, setAguaBloqueada] = useState<boolean>(true);
  const [proporcaoBloqueada, setProporcaoBloqueada] = useState<boolean>(true);

  // Estados do cronômetro
  const [tempo, setTempo] = useState<number>(0); // em segundos
  const [rodando, setRodando] = useState<boolean>(false);
  const [contandoRegressivo, setContandoRegressivo] = useState<boolean>(false);
  const [contagemRegressiva, setContagemRegressiva] = useState<number>(3);
  const [modalAberto, setModalAberto] = useState<boolean>(false);
  const [sidebarAberta, setSidebarAberta] = useState<boolean>(false);
  const [sidebarFechando, setSidebarFechando] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const regressivaRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const finishAudioRef = useRef<HTMLAudioElement | null>(null);
  const transitionAudioRef = useRef<HTMLAudioElement | null>(null);
  const passoAnteriorRef = useRef<number>(-1);
  const receitaRef = useRef<HTMLDivElement | null>(null);
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Funções de armazenamento local
  const salvarPreferencias = useCallback(() => {
    const preferencias = {
      cafe,
      proporcao,
      perfilSabor,
      perfilCorpo,
      aguaBloqueada,
      proporcaoBloqueada
    };
    localStorage.setItem('cereja-preferencias', JSON.stringify(preferencias));
  }, [cafe, proporcao, perfilSabor, perfilCorpo, aguaBloqueada, proporcaoBloqueada]);

  const carregarPreferencias = useCallback(() => {
    try {
      const preferenciasString = localStorage.getItem('cereja-preferencias');
      if (preferenciasString) {
        const preferencias = JSON.parse(preferenciasString);
        setCafe(preferencias.cafe || "15");
        setProporcao(preferencias.proporcao || "15");
        setPerfilSabor(preferencias.perfilSabor || "Equilibrado");
        setPerfilCorpo(preferencias.perfilCorpo || "Equilibrado");
        setAguaBloqueada(preferencias.aguaBloqueada ?? true);
        setProporcaoBloqueada(preferencias.proporcaoBloqueada ?? true);

        // Recalcular água com os valores carregados
        const numCafe = parseFloat(preferencias.cafe || "15");
        const numProporcao = parseFloat(preferencias.proporcao || "15");
        if (numCafe > 0 && numProporcao > 0) {
          setAgua(arredondar(numCafe * numProporcao));
        }
      }
    } catch (error) {
      console.error('Erro ao carregar preferências:', error);
    }
  }, []); // Sem dependências pois só é chamada uma vez na inicialização

  // Carregar preferências ao inicializar
  useEffect(() => {
    carregarPreferencias();
  }, [carregarPreferencias]);

  // Função para fechar sidebar com animação
  const fecharSidebar = useCallback(() => {
    setSidebarFechando(true);
    setTimeout(() => {
      setSidebarAberta(false);
      setSidebarFechando(false);
    }, 300);
  }, []);

  // Fechar sidebar ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        fecharSidebar();
      }
    };

    if (sidebarAberta) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarAberta, fecharSidebar]);

  // Salvar preferências quando houver mudanças
  useEffect(() => {
    salvarPreferencias();
  }, [salvarPreferencias]);

  // Função para arredondar valores
  const arredondar = (valor: number): string => {
    return Math.round(valor) + "";
  };

  // Função para verificar se os inputs devem estar desabilitados
  const inputsDesabilitados = () => {
    return rodando || contandoRegressivo || tempo > 0;
  };

  // Função para atualizar café e recalcular água
  const handleCafeChange = (valor: string) => {
    if (inputsDesabilitados()) return;
    setCafe(valor);
    const numCafe = parseFloat(valor) || 0;
    const numProporcao = parseFloat(proporcao) || 0;
    if (numCafe > 0 && numProporcao > 0) {
      setAgua(arredondar(numCafe * numProporcao));
    }
  };

  // Função para atualizar água e recalcular café
  const handleAguaChange = (valor: string) => {
    if (aguaBloqueada || inputsDesabilitados()) return;
    setAgua(valor);
    const numAgua = parseFloat(valor) || 0;
    const numProporcao = parseFloat(proporcao) || 0;
    if (numAgua > 0 && numProporcao > 0) {
      setCafe(arredondar(numAgua / numProporcao));
    }
  };

  // Função para atualizar proporção e recalcular água
  const handleProporcaoChange = (valor: string) => {
    if (proporcaoBloqueada || inputsDesabilitados()) return;
    setProporcao(valor);
    const numCafe = parseFloat(cafe) || 0;
    const numProporcao = parseFloat(valor) || 0;
    if (numCafe > 0 && numProporcao > 0) {
      setAgua(arredondar(numCafe * numProporcao));
    }
  };

  // Função para selecionar todo o texto ao clicar
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  // Função para desabilitar scroll e setas nos inputs
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "ArrowUp" || e.key === "ArrowDown") {
      e.preventDefault();
    }
  };

  const handleWheel = (e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  };

  // Função para calcular a receita baseada no método 4:6
  const calcularReceita = useCallback(() => {
    const aguaTotal = parseFloat(agua) || 0;
    if (aguaTotal <= 0) return [];

    const receita = [];
    let aguaAcumulada = 0;

    // Primeiros 40% (2 despejos)
    const primeiros40 = aguaTotal * 0.4;
    let primeiro, segundo;

    if (perfilSabor === "Mais Acidez") {
      primeiro = Math.round(aguaTotal * 0.24);
      segundo = Math.round(aguaTotal * 0.16);
    } else if (perfilSabor === "Mais Doçura") {
      primeiro = Math.round(aguaTotal * 0.16);
      segundo = Math.round(aguaTotal * 0.24);
    } else { // Equilibrado
      primeiro = Math.round(primeiros40 / 2);
      segundo = Math.round(primeiros40 / 2);
    }

    // Adicionar primeiros dois despejos
    aguaAcumulada += primeiro;
    receita.push({
      tempo: "00:00",
      tempoSegundos: 0,
      quantidade: primeiro,
      total: aguaAcumulada
    });

    aguaAcumulada += segundo;
    receita.push({
      tempo: "00:45",
      tempoSegundos: 45,
      quantidade: segundo,
      total: aguaAcumulada
    });

    // Últimos 60%
    const ultimos60 = aguaTotal - aguaAcumulada;
    let despejos = [];

    if (perfilCorpo === "Menos Corpo") {
      // 2 despejos (30% cada)
      const despejo = Math.round(ultimos60 / 2);
      despejos = [despejo, ultimos60 - despejo];
    } else if (perfilCorpo === "Mais Corpo") {
      // 4 despejos (15% cada)
      const despejo = Math.round(ultimos60 / 4);
      despejos = [despejo, despejo, despejo, ultimos60 - (despejo * 3)];
    } else { // Equilibrado
      // 3 despejos (20% cada)
      const despejo = Math.round(ultimos60 / 3);
      despejos = [despejo, despejo, ultimos60 - (despejo * 2)];
    }

    // Adicionar despejos restantes
    const tempos = ["01:30", "02:15", "03:00", "03:45"];
    const temposSegundos = [90, 135, 180, 225];
    despejos.forEach((quantidade, index) => {
      aguaAcumulada += quantidade;
      receita.push({
        tempo: tempos[index],
        tempoSegundos: temposSegundos[index],
        quantidade,
        total: aguaAcumulada
      });
    });

    return receita;
  }, [agua, perfilSabor, perfilCorpo]);

  // Função para calcular o tempo total da receita
  const calcularTempoTotal = useCallback(() => {
    const receita = calcularReceita();
    if (receita.length === 0) return 0;

    const ultimoPasso = receita[receita.length - 1];
    return ultimoPasso.tempoSegundos + 45;
  }, [calcularReceita]);

  // Função para determinar o passo atual baseado no tempo do cronômetro
  const obterPassoAtual = useCallback(() => {
    const receita = calcularReceita();
    if (!rodando && tempo === 0) return -1; // Nenhum passo ativo se não iniciou

    // Encontrar o último passo que já deveria ter sido executado
    let passoAtual = -1;
    for (let i = 0; i < receita.length; i++) {
      if (tempo >= receita[i].tempoSegundos) {
        passoAtual = i;
      } else {
        break;
      }
    }

    return passoAtual;
  }, [calcularReceita, rodando, tempo]);

  // Funções do cronômetro
  useEffect(() => {
    if (rodando) {
      intervalRef.current = setInterval(() => {
        setTempo(prev => {
          const novoTempo = prev + 1;
          const tempoTotal = calcularTempoTotal();

          // Parar o cronômetro quando atingir o tempo total
          if (novoTempo >= tempoTotal) {
            setRodando(false);
            // Tracking da receita finalizada
            const receita = calcularReceita();
            trackRecipeCompleted(tempoTotal, receita.length);
            // Reproduzir áudio de finalização
            if (finishAudioRef.current) {
              finishAudioRef.current.currentTime = 0;
              finishAudioRef.current.play().catch(console.error);
            }
            return tempoTotal;
          }

          return novoTempo;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [rodando, calcularTempoTotal, calcularReceita]);

  // useEffect para contagem regressiva
  useEffect(() => {
    if (contandoRegressivo) {
      if (contagemRegressiva > 0) {
        regressivaRef.current = setTimeout(() => {
          setContagemRegressiva(prev => prev - 1);
        }, 1000);
      } else {
        setContandoRegressivo(false);
        setRodando(true);
        setContagemRegressiva(3); // Reset para próxima vez

        // Rolar para a receita em dispositivos mobile
        if (receitaRef.current && window.innerWidth < 768) {
          setTimeout(() => {
            receitaRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }, 100);
        }
      }
    }

    return () => {
      if (regressivaRef.current) {
        clearTimeout(regressivaRef.current);
      }
    };
  }, [contandoRegressivo, contagemRegressiva]);

  // useEffect para inicializar os áudios
  useEffect(() => {
    audioRef.current = new Audio('/audio/countdown.mp3');
    audioRef.current.preload = 'auto';

    finishAudioRef.current = new Audio('/audio/finish.mp3');
    finishAudioRef.current.preload = 'auto';

    transitionAudioRef.current = new Audio('/audio/transition.mp3');
    transitionAudioRef.current.preload = 'auto';
    transitionAudioRef.current.volume = 0.5; // Reduzir volume

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (finishAudioRef.current) {
        finishAudioRef.current.pause();
        finishAudioRef.current = null;
      }
      if (transitionAudioRef.current) {
        transitionAudioRef.current.pause();
        transitionAudioRef.current = null;
      }
    };
  }, []);

  const iniciarPausar = () => {
    if (rodando) {
      setRodando(false);
      trackRecipeTimer('pause', tempo);
      trackButtonClick('pausar_receita', 'calculadora');
    } else if (contandoRegressivo) {
      // Cancelar contagem regressiva
      setContandoRegressivo(false);
      setContagemRegressiva(3);
      trackRecipeTimer('reset');
      trackButtonClick('cancelar_contagem', 'calculadora');
      // Parar os áudios se estiverem tocando
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      if (finishAudioRef.current) {
        finishAudioRef.current.pause();
        finishAudioRef.current.currentTime = 0;
      }
      if (transitionAudioRef.current) {
        transitionAudioRef.current.pause();
        transitionAudioRef.current.currentTime = 0;
      }
    } else {
      // Se já foi iniciado antes (tempo > 0), iniciar diretamente sem contagem regressiva
      if (tempo > 0) {
        setRodando(true);
        trackRecipeTimer('resume', tempo);
        trackButtonClick('retomar_receita', 'calculadora');
        // Rolar para a receita em dispositivos mobile ao retomar
        if (receitaRef.current && window.innerWidth < 768) {
          setTimeout(() => {
            receitaRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }, 100);
        }
      } else {
        // Primeira vez: iniciar contagem regressiva
        setContandoRegressivo(true);
        trackRecipeGenerated({
          cafe: parseFloat(cafe),
          agua: parseFloat(agua),
          proporcao: parseFloat(proporcao),
          perfilSabor,
          perfilCorpo
        });
        trackRecipeTimer('start');
        trackButtonClick('iniciar_receita', 'calculadora');
        // Reproduzir áudio do countdown
        if (audioRef.current) {
          audioRef.current.currentTime = 0;
          audioRef.current.play().catch(console.error);
        }
      }
    }
  };

  const resetar = () => {
    trackRecipeTimer('reset', tempo);
    trackButtonClick('reiniciar_receita', 'calculadora');
    setRodando(false);
    setContandoRegressivo(false);
    setTempo(0);
    setContagemRegressiva(3);
    passoAnteriorRef.current = -1; // Reset do passo anterior
    // Parar os áudios se estiverem tocando
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    if (finishAudioRef.current) {
      finishAudioRef.current.pause();
      finishAudioRef.current.currentTime = 0;
    }
    if (transitionAudioRef.current) {
      transitionAudioRef.current.pause();
      transitionAudioRef.current.currentTime = 0;
    }
  };

  // useEffect para detectar mudanças de passo e reproduzir áudio de transição
  useEffect(() => {
    const passoAtual = obterPassoAtual();

    // Se mudou de passo e não é o primeiro passo (evita tocar no passo 0)
    if (passoAtual !== passoAnteriorRef.current && passoAtual > passoAnteriorRef.current && passoAtual > 0) {
      // Reproduzir áudio de transição
      if (transitionAudioRef.current) {
        transitionAudioRef.current.currentTime = 0;
        transitionAudioRef.current.play().catch(console.error);
      }
    }

    // Atualizar referência do passo anterior
    passoAnteriorRef.current = passoAtual;
  }, [tempo, rodando, calcularReceita]); // Mudei as dependências para ser mais explícito

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header com menu hambúrguer pequeno */}
      <header className="fixed sm:absolute top-4 left-4 sm:top-6 sm:left-6 z-30">
        <button
          onClick={() => setSidebarAberta(true)}
          className="flex items-center gap-2 p-2 text-muted-foreground hover:text-primary hover:bg-accent/50 rounded-lg sm:rounded-lg rounded-full sm:bg-transparent bg-card/90 backdrop-blur-sm shadow-lg sm:shadow-none border sm:border-0 border-border/20 transition-all"
        >
          <Menu size={20} />
          <span className="text-sm font-medium hidden sm:inline">Menu</span>
        </button>
      </header>

      <Sidebar
        isOpen={sidebarAberta}
        isClosing={sidebarFechando}
        onClose={fecharSidebar}
        ref={sidebarRef}
      />

      {/* Conteúdo principal */}
      <div className="flex flex-col items-center justify-start pt-2 sm:pt-4 px-4 sm:px-8 gap-4 sm:gap-8 flex-1">
        {/* Título grande centralizado */}
        <div className="flex items-center gap-2 sm:gap-3 mt-4 sm:mt-2">
          <Image
            src="/cereja.png"
            alt="Logo Cereja - Calculadora do método 4:6 de Tetsu Kasuya para café coado"
            width={80}
            height={80}
            priority
            className="object-contain sm:w-[100px] sm:h-[100px]"
            quality={90}
          />
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl font-bold text-foreground">
              cereja
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1 sm:mt-2">
              calculadora do método 4:6
            </p>
          </div>
        </div>

      {/* Seção inferior: duas colunas responsivas */}
      <div className="w-full max-w-6xl grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8 items-start">
        <div className="bg-card p-4 sm:p-6 rounded-lg border border-border shadow-md">
          {/* Campos de entrada numéricos */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
            <div>
              <label className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
                Café (g)
              </label>
              <input
                type="number"
                value={cafe}
                onChange={(e) => handleCafeChange(e.target.value)}
                onFocus={handleFocus}
                onKeyDown={handleKeyDown}
                onWheel={handleWheel}
                min="0"
                step="0.1"
                disabled={inputsDesabilitados()}
                className={`w-full px-3 py-2.5 sm:py-2 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
                  inputsDesabilitados()
                    ? "bg-muted text-muted-foreground cursor-not-allowed"
                    : "bg-background"
                }`}
              />
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
                Água (ml)
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={agua}
                  onChange={(e) => handleAguaChange(e.target.value)}
                  onFocus={handleFocus}
                  onKeyDown={handleKeyDown}
                  onWheel={handleWheel}
                  min="0"
                  step="0.1"
                  disabled={aguaBloqueada || inputsDesabilitados()}
                  className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
                    aguaBloqueada || inputsDesabilitados()
                      ? "bg-muted text-muted-foreground cursor-not-allowed"
                      : "bg-background"
                  }`}
                />
                <button
                  type="button"
                  onClick={() => !inputsDesabilitados() && setAguaBloqueada(!aguaBloqueada)}
                  disabled={inputsDesabilitados()}
                  className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target ${
                    inputsDesabilitados()
                      ? "text-muted-foreground cursor-not-allowed"
                      : "text-muted-foreground hover:text-foreground active:text-primary"
                  }`}
                >
                  {aguaBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
                Proporção (1:{proporcao})
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={proporcao}
                  onChange={(e) => handleProporcaoChange(e.target.value)}
                  onFocus={handleFocus}
                  onKeyDown={handleKeyDown}
                  onWheel={handleWheel}
                  min="0"
                  step="0.1"
                  disabled={proporcaoBloqueada || inputsDesabilitados()}
                  className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
                    proporcaoBloqueada || inputsDesabilitados()
                      ? "bg-muted text-muted-foreground cursor-not-allowed"
                      : "bg-background"
                  }`}
                />
                <button
                  type="button"
                  onClick={() => !inputsDesabilitados() && setProporcaoBloqueada(!proporcaoBloqueada)}
                  disabled={inputsDesabilitados()}
                  className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target ${
                    inputsDesabilitados()
                      ? "text-muted-foreground cursor-not-allowed"
                      : "text-muted-foreground hover:text-foreground active:text-primary"
                  }`}
                >
                  {proporcaoBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
                </button>
              </div>
            </div>
          </div>

          {/* Perfil de Sabor */}
          <div className="mb-4 sm:mb-6">
            <h3 className="text-xs sm:text-sm font-medium text-foreground mb-2 sm:mb-3">
              Perfil de Sabor
            </h3>
            <div className="grid grid-cols-3 gap-2 sm:gap-3">
              {[
                { nome: "Mais Acidez", icone: "/images/acido.png", alt: "Ícone representando perfil de sabor com mais acidez no café" },
                { nome: "Equilibrado", icone: "/images/equilibrado.png", alt: "Ícone representando perfil de sabor equilibrado no café" },
                { nome: "Mais Doçura", icone: "/images/docura.png", alt: "Ícone representando perfil de sabor com mais doçura no café" }
              ].map((opcao) => (
                <ProfileButton
                  key={opcao.nome}
                  opcao={opcao}
                  isSelected={perfilSabor === opcao.nome}
                  isDisabled={inputsDesabilitados()}
                  onClick={() => !inputsDesabilitados() && setPerfilSabor(opcao.nome)}
                />
              ))}
            </div>
          </div>

          {/* Perfil de Corpo */}
          <div>
            <h3 className="text-xs sm:text-sm font-medium text-foreground mb-2 sm:mb-3">
              Perfil de Corpo
            </h3>
            <div className="grid grid-cols-3 gap-2 sm:gap-3">
              {[
                { nome: "Menos Corpo", icone: "/images/menos-corpo.png", alt: "Ícone representando perfil de corpo mais leve no café" },
                { nome: "Equilibrado", icone: "/images/equilibrado.png", alt: "Ícone representando perfil de corpo equilibrado no café" },
                { nome: "Mais Corpo", icone: "/images/mais-corpo.png", alt: "Ícone representando perfil de corpo mais encorpado no café" }
              ].map((opcao) => (
                <ProfileButton
                  key={opcao.nome}
                  opcao={opcao}
                  isSelected={perfilCorpo === opcao.nome}
                  isDisabled={inputsDesabilitados()}
                  onClick={() => !inputsDesabilitados() && setPerfilCorpo(opcao.nome)}
                />
              ))}
            </div>
          </div>
        </div>

        <Timer
          tempo={tempo}
          rodando={rodando}
          contandoRegressivo={contandoRegressivo}
          contagemRegressiva={contagemRegressiva}
          tempoTotal={calcularTempoTotal()}
          modalAberto={modalAberto}
          onIniciarPausar={iniciarPausar}
          onResetar={resetar}
          onAbrirModal={() => {
            setModalAberto(true);
            trackButtonClick('abrir_compartilhar', 'calculadora');
          }}
        />

          <ProgressBar
            tempo={tempo}
            tempoTotal={calcularTempoTotal()}
            receita={calcularReceita()}
            passoAtual={obterPassoAtual()}
            rodando={rodando}
            contandoRegressivo={contandoRegressivo}
          />

          <ShareModal
            isOpen={modalAberto}
            onClose={() => setModalAberto(false)}
            cafe={cafe}
            agua={agua}
            proporcao={proporcao}
            perfilSabor={perfilSabor}
            perfilCorpo={perfilCorpo}
            receita={calcularReceita()}
          />

          <RecipeSteps
            receita={calcularReceita()}
            passoAtual={obterPassoAtual()}
            tempo={tempo}
            tempoTotal={calcularTempoTotal()}
            contandoRegressivo={contandoRegressivo}
            ref={receitaRef}
          />
        </div>
      </div>
      </div>

      <Footer />
    </div>
  );
}

export default Home;
