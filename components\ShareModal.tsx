"use client";

import { memo } from "react";
import { trackRecipeShared, trackButtonClick } from "@/lib/analytics";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  cafe: string;
  agua: string;
  proporcao: string;
  perfilSabor: string;
  perfilCorpo: string;
  receita: Array<{
    tempo: string;
    quantidade: number;
    total: number;
  }>;
}

const ShareModal = memo(function ShareModal({
  isOpen,
  onClose,
  cafe,
  agua,
  proporcao,
  perfilSabor,
  perfilCorpo,
  receita,
}: ShareModalProps) {
  if (!isOpen) return null;

  const handleCopyRecipe = () => {
    const texto = `☕ Receita Cereja - Método 4:6\n\n` +
      `📊 Configuração:\n` +
      `• ${cafe}g de café\n` +
      `• ${agua}ml de água\n` +
      `• Proporção 1:${proporcao}\n` +
      `• Perfil: ${perfilSabor} / ${perfilCorpo}\n\n` +
      `⏱️ Passos:\n` +
      receita.map((passo, i) =>
        `${i + 1}. ${passo.tempo} - ${passo.quantidade}ml (Total: ${passo.total}ml)`
      ).join('\n') +
      `\n\n🌐 cereja-app.vercel.app`;

    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(texto).then(() => {
        trackRecipeShared('copy');
        trackButtonClick('copiar_receita', 'modal_compartilhar');
        onClose();
        showFeedback('Receita copiada! ✅');
      }).catch(() => {
        alert('Não foi possível copiar a receita.');
      });
    } else {
      // Fallback para navegadores mais antigos
      const textArea = document.createElement('textarea');
      textArea.value = texto;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        trackRecipeShared('copy');
        trackButtonClick('copiar_receita_fallback', 'modal_compartilhar');
        onClose();
        showFeedback('Receita copiada! ✅');
      } catch {
        alert('Não foi possível copiar a receita.');
      }
      document.body.removeChild(textArea);
    }
  };

  const showFeedback = (message: string) => {
    const feedback = document.createElement('div');
    feedback.textContent = message;
    feedback.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top duration-300';
    document.body.appendChild(feedback);
    setTimeout(() => {
      feedback.remove();
    }, 3000);
  };

  const handleCancel = () => {
    onClose();
    trackButtonClick('cancelar_compartilhar', 'modal_compartilhar');
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 animate-in fade-in duration-300">
      <div className="bg-card border border-border rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto animate-in zoom-in-95 duration-300">
        {/* Header do Modal */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h3 className="text-lg font-semibold text-card-foreground">
            Compartilhar Receita
          </h3>
          <button
            onClick={onClose}
            className="p-1 rounded-md hover:bg-accent transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        {/* Conteúdo do Modal */}
        <div className="p-4 space-y-4">
          {/* Preview da Receita */}
          <div className="bg-secondary rounded-lg p-4 text-sm">
            <div className="font-semibold text-secondary-foreground mb-2">
              ☕ Receita Cereja - Método 4:6
            </div>
            <div className="space-y-2 text-muted-foreground">
              <div>
                <strong>📊 Configuração:</strong>
                <div className="ml-2">
                  • {cafe}g de café<br/>
                  • {agua}ml de água<br/>
                  • Proporção 1:{proporcao}<br/>
                  • Perfil: {perfilSabor} / {perfilCorpo}
                </div>
              </div>
              <div>
                <strong>⏱️ Passos:</strong>
                <div className="ml-2">
                  {receita.map((passo, i) => (
                    <div key={i}>
                      {i + 1}. {passo.tempo} - {passo.quantidade}ml (Total: {passo.total}ml)
                    </div>
                  ))}
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                🌐 cereja-app.vercel.app
              </div>
            </div>
          </div>

          {/* Botões de Ação */}
          <div className="flex gap-2">
            <button
              onClick={handleCopyRecipe}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              Copiar Receita
            </button>
            <button
              onClick={handleCancel}
              className="px-4 py-3 bg-secondary text-secondary-foreground rounded-md text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

export default ShareModal;
