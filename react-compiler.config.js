const ReactCompilerConfig = {
  // Configuração mais conservadora para evitar problemas de sintaxe
  compilationMode: 'infer',

  // Configurações de otimização - excluir arquivo problemático
  sources: (filename) => {
    // Excluir o arquivo page.tsx principal que está causando problemas
    if (filename.includes('app/page.tsx')) {
      return false;
    }
    // Incluir todos os outros arquivos
    return true;
  },

  // Configurações de panic threshold
  panicThreshold: 'ALL_ERRORS',

  // Configurações específicas para componentes complexos
  enableTreatRefLikeIdentifierInJSX: true,
  enablePreserveExistingMemoizationGuarantees: true,

  // Configurações de debugging
  logger: {
    logEvent: (filename, event) => {
      if (event.kind === 'CompileError') {
        console.warn(`React Compiler warning in ${filename}:`, event.detail);
      }
    },
  },
};

module.exports = ReactCompilerConfig;
