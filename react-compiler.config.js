const ReactCompilerConfig = {
  // Configuração mais conservadora para evitar problemas de sintaxe
  compilationMode: 'infer',

  // Configurações de otimização - excluir arquivos problemáticos
  sources: (filename) => {
    // Excluir arquivos que estão causando problemas com o React Compiler
    if (filename.includes('app/page.tsx') || filename.includes('app/not-found.tsx')) {
      return false;
    }
    // Incluir todos os outros arquivos
    return true;
  },

  // Configurações de panic threshold
  panicThreshold: 'ALL_ERRORS',

  // Configurações específicas para componentes complexos
  enableTreatRefLikeIdentifierInJSX: true,
  enablePreserveExistingMemoizationGuarantees: true,

  // Configurações de debugging simplificadas
  logger: {
    logEvent: (filename, event) => {
      // Verificar se event e event.kind existem antes de usar
      if (event && event.kind === 'CompileError') {
        console.warn(`React Compiler warning in ${filename}:`, event.detail || event);
      }
    },
  },
};

module.exports = ReactCompilerConfig;
