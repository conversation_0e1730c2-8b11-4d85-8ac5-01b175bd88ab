"use client";

import { memo } from "react";
import Image from "next/image";

interface ProfileOption {
  nome: string;
  icone: string;
  alt: string;
}

interface ProfileButtonProps {
  opcao: ProfileOption;
  isSelected: boolean;
  isDisabled: boolean;
  onClick: () => void;
}

const ProfileButton = memo(function ProfileButton({
  opcao,
  isSelected,
  isDisabled,
  onClick,
}: ProfileButtonProps) {
  return (
    <button
      onClick={onClick}
      disabled={isDisabled}
      className={`px-3 sm:px-8 py-3 sm:py-4 rounded-md text-xs sm:text-sm font-medium transition-colors flex flex-col items-center gap-2 min-h-touch touch-target ${
        isDisabled
          ? "cursor-not-allowed opacity-50"
          : "cursor-pointer"
      } ${
        isSelected
          ? "bg-primary text-primary-foreground shadow-sm"
          : "bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground shadow-xs hover:shadow-sm"
      }`}
    >
      <Image
        src={opcao.icone}
        alt={opcao.alt}
        width={32}
        height={32}
        className="object-contain sm:w-[30px] sm:h-[30px]"
        quality={75}
        loading="lazy"
      />
      {opcao.nome}
    </button>
  );
});

export default ProfileButton;
